#!/bin/bash

# ForaChat Integration Test Runner Helper Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
PROMPTS_FILE=""
LOG_LEVEL="normal"
MAX_WAIT=30
OUTPUT_FILE=""

# Function to show usage
show_usage() {
    echo -e "${BLUE}ForaChat Integration Test Runner Helper${NC}"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -f, --file <file>     Prompts file (required)"
    echo "  -l, --log-level <level>  Log level: verbose, normal, minimal (default: normal)"
    echo "  -w, --wait <seconds>  Max wait time for delayed messages (default: 30)"
    echo "  -o, --output <file>   Output file for results"
    echo "  -h, --help           Show this help"
    echo ""
    echo "Predefined test scenarios:"
    echo "  $0 quick             Run quick test (2 prompts)"
    echo "  $0 basic             Run basic test suite (15 prompts)"
    echo "  $0 structured        Run structured test suite (10 prompts with validation)"
    echo "  $0 verbose           Run basic tests with verbose logging"
    echo ""
    echo "Examples:"
    echo "  $0 -f test-prompts/basic-prompts.txt"
    echo "  $0 quick -l verbose"
    echo "  $0 structured -w 60 -o my-results.json"
}

# Function to run tests
run_test() {
    local prompts_file="$1"
    local extra_args="$2"
    
    if [ ! -f "$prompts_file" ]; then
        echo -e "${RED}Error: Prompts file not found: $prompts_file${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}🚀 Starting ForaChat Integration Tests${NC}"
    echo -e "${YELLOW}Prompts file: $prompts_file${NC}"
    echo -e "${YELLOW}Log level: $LOG_LEVEL${NC}"
    echo -e "${YELLOW}Max wait time: ${MAX_WAIT}s${NC}"
    echo ""
    
    # Build the command
    local cmd="npm run test:integration-live -- --prompts-file \"$prompts_file\" --server-url localhost --server-port 3000 --log-level $LOG_LEVEL --max-delayed-wait $MAX_WAIT"

    if [ -n "$OUTPUT_FILE" ]; then
        cmd="$cmd --output-file \"$OUTPUT_FILE\""
    fi

    if [ -n "$extra_args" ]; then
        cmd="$cmd $extra_args"
    fi
    
    echo -e "${BLUE}Running: $cmd${NC}"
    echo ""
    
    # Execute the command
    eval $cmd
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        echo -e "${GREEN}✅ Tests completed successfully!${NC}"
    else
        echo -e "${RED}❌ Tests failed with exit code $exit_code${NC}"
        exit $exit_code
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--file)
            PROMPTS_FILE="$2"
            shift 2
            ;;
        -l|--log-level)
            LOG_LEVEL="$2"
            shift 2
            ;;
        -w|--wait)
            MAX_WAIT="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        quick)
            PROMPTS_FILE="test-prompts/quick-test.txt"
            shift
            ;;
        basic)
            PROMPTS_FILE="test-prompts/basic-prompts.txt"
            shift
            ;;
        structured)
            PROMPTS_FILE="test-prompts/structured-prompts.json"
            shift
            ;;
        verbose)
            PROMPTS_FILE="test-prompts/basic-prompts.txt"
            LOG_LEVEL="verbose"
            shift
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            show_usage
            exit 1
            ;;
    esac
done

# Check if prompts file is specified
if [ -z "$PROMPTS_FILE" ]; then
    echo -e "${RED}Error: Prompts file is required${NC}"
    echo ""
    show_usage
    exit 1
fi

# Run the test
run_test "$PROMPTS_FILE"
