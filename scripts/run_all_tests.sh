#!/bin/bash

# This script runs all test prompt files in the test-prompts directory.

set -e

# Get the directory of the script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$SCRIPT_DIR/.."
TEST_PROMPTS_DIR="$PROJECT_ROOT/test-prompts"
TEST_LOGS_DIR="$PROJECT_ROOT/test-logs"

# Create the test-logs directory if it doesn't exist
mkdir -p "$TEST_LOGS_DIR"

# Find all .txt and .json files in the test-prompts directory
for prompt_file in "$TEST_PROMPTS_DIR"/*.txt "$TEST_PROMPTS_DIR"/*.json; do
  if [ -f "$prompt_file" ]; then
    # Get the base name of the prompt file
    base_name=$(basename "$prompt_file")
    # Remove the extension to get the test name
    test_name="${base_name%.*}"
    # Define the output file path
    output_file="$TEST_LOGS_DIR/$test_name.json"
    
    echo "Running test for $base_name..."
    # Run the test using the existing run-tests.sh script
    "$SCRIPT_DIR/run-tests.sh" -f "$prompt_file" -o "$output_file"
    echo "Test for $base_name finished. Log saved to $output_file"
    echo "--------------------------------------------------"
  fi
done

echo "All tests completed."
